# Stage 1: Build
FROM node:22 AS build

WORKDIR /app

COPY package.json package-lock.json ./
RUN npm install --registry=https://registry.npmmirror.com

COPY . .
RUN npm run build

# Stage 2: Run
FROM nginx:alpine

COPY nginx.conf.template /etc/nginx/templates/default.conf.template
COPY --from=build /app/dist /usr/share/nginx/html

ENV PORT 80
ENV HTTPS_PORT 443
ENV SERVER_URL http://openeva-server:8080

EXPOSE 80
