server {
    listen ${PORT};
    server_name ${DOMAIN};

    location ^~/api/ {
        rewrite ^/api/(.*)$ /$1 break;
        proxy_pass ${SERVER_URL};
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location / {
        root   /usr/share/nginx/html;
        index  index.html;
        try_files $uri $uri/ /index.html;
    }

    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }

    location ~ /\.ht {
        deny all;
    }
}