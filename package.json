{"name": "openeva-ui", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "test:unit": "vitest", "test:e2e": "start-server-and-test preview http://localhost:4173 'cypress run --e2e'", "test:e2e:dev": "start-server-and-test 'vite dev --port 4173' http://localhost:4173 'cypress open --e2e'", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@types/js-cookie": "^3.0.6", "@types/moment": "^2.11.29", "axios": "^1.7.9", "element-plus": "^2.9.3", "js-cookie": "^3.0.5", "marked": "^15.0.7", "moment": "^2.30.1", "pinia": "^2.3.0", "qs": "^6.14.0", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@tsconfig/node22": "^22.0.0", "@types/jsdom": "^21.1.7", "@types/node": "^22.10.2", "@types/qs": "^6.9.18", "@vitejs/plugin-vue": "^5.2.1", "@vitest/eslint-plugin": "1.1.20", "@vue/eslint-config-prettier": "^10.1.0", "@vue/eslint-config-typescript": "^14.1.3", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "cypress": "^13.17.0", "eslint": "^9.14.0", "eslint-plugin-cypress": "^4.1.0", "eslint-plugin-vue": "^9.30.0", "jsdom": "^25.0.1", "npm-run-all2": "^7.0.2", "prettier": "^3.3.3", "sass": "^1.83.4", "sass-embedded": "^1.83.4", "start-server-and-test": "^2.0.9", "typescript": "~5.6.3", "unplugin-auto-import": "^19.1.1", "unplugin-icons": "^22.1.0", "unplugin-vue-components": "^28.0.0", "vite": "^6.0.5", "vite-plugin-vue-devtools": "^7.6.8", "vitest": "^2.1.8", "vue-tsc": "^2.1.10"}}