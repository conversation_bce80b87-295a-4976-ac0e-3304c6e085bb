import api from '@/utils/axios'
import type { CommonResp } from '@/types/resp'
import type { RepoListReqParams, RepoListResp, RepoDetail, RepoTagsReqParams } from '@/types/repo'

export const getRepoList = (params: RepoListReqParams) => {
  return api.get<RepoListResp, CommonResp<RepoListResp>, RepoListReqParams>('/repository/list', {
    params,
  })
}

export const getRepoDetail = (id: number) => {
  return api.get<RepoDetail, CommonResp<RepoDetail>>(`/repository/${id}`)
}

export const getRepoTags = (params: RepoTagsReqParams) => {
  return api.get<RepoListResp, CommonResp<RepoListResp>, RepoTagsReqParams>('/repository/tags', {
    params,
  })
}

