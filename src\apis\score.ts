import api from '@/utils/axios'
import type { CommonResp } from '@/types/resp'

export const getScoreAverage = (repoId: number) => {
  return api.post<{
    repoId: number,
    technologicalLevel: number,
    documentIntegrity: number,
    communityActivityLevel: number,
    codeQuality: number
  }, CommonResp<any>>('/score/getAverage', { repoId })
}

export const addScore = (params: {
  repoId: number,
  technologicalLevel: number,
  documentIntegrity: number,
  communityActivityLevel: number,
  codeQuality: number
}) => {
  return api.post<boolean, CommonResp<boolean>>('/score/add', params)
} 