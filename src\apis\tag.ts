import api from '@/utils/axios'
import type { CommonResp } from '@/types/resp'

export const getTagNames = (repoId: number) => {
  return api.get<string[], CommonResp<string[]>>('/tag/getTagNames', {
    params: { repoId }
  })
}

export const addTagAndRepoTag = (repoId: number, tagName: string) => {
  return api.post<number, CommonResp<number>>('/tag/addTagAndRepoTag', {
    repoId,
    tagName
  })
}

export const getPendingTagNames = (repoId: number) => {
  return api.get<string[], CommonResp<string[]>>('/tag/pendingTagNames', {
    params: { repoId }
  })
}

export const deleteTagAndRepoTag = (repoId: number, tagName: string) => {
  return api.post<number, CommonResp<number>>('/tag/deleteTagAndRepoTag', {
    repoId,
    tagName
  })
}

// 审核相关接口
export const getPendingTags = () => {
  return api.get<any[], CommonResp<any[]>>('/tag/pendingTags')
}

export const approveTag = (data: { repoId: string; tagId: string }) => {
  return api.post<any, CommonResp<any>>('/tag/approveTag', data)
}

export const rejectTag = (data: { repoId: string; tagId: string }) => {
  return api.post<any, CommonResp<any>>('/tag/rejectTag', data)
} 