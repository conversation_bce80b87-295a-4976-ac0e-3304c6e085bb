import api from '@/utils/axios'
import type { CommonResp } from '@/types/resp'
import type {
  User,
  GetUserByIdReq,
  GetUserByUsernameReq,
  UpdateUserReq,
  UpdateUserAvatarReq,
  ResetUserPasswordReq,
  UserResp
} from '@/types/user'

// 根据用户ID获取用户信息
export const getUserById = (id: number) => {
  return api.get<UserResp, CommonResp<UserResp>>('/user/selectUserById', {
    params: { id }
  })
}

// 根据用户名获取用户信息
export const getUserByUsername = (username: string) => {
  return api.get<UserResp, CommonResp<UserResp>>('/user/selectUserByUserName', {
    params: { username }
  })
}

// 更新用户信息
export const updateUser = (userId: number, user: Partial<User>) => {
  return api.post<UserResp, CommonResp<UserResp>>(`/user/updateUser?userId=${userId}`, user)
}

// 更新用户头像
export const updateUserAvatar = (userId: number, avatar: string) => {
  return api.post<boolean, CommonResp<boolean>>(`/user/updateUserAvatar?userId=${userId}&avatar=${encodeURIComponent(avatar)}`)
}

// 重置用户密码
export const resetUserPassword = (userId: number, oldPwd: string, newPwd: string) => {
  return api.post<boolean, CommonResp<boolean>>(`/user/resetUserPwd?userId=${userId}&oldPwd=${oldPwd}&newPwd=${newPwd}`)
}

// 获取当前登录用户信息的辅助函数
export const getCurrentUser = async (): Promise<UserResp | null> => {
  try {
    // 从token中解析用户ID
    const { getUserIdFromToken } = await import('@/utils/auth')
    const userId = getUserIdFromToken()

    if (!userId) {
      console.warn('无法从token中获取用户ID')
      return null
    }

    // 调用getUserById接口获取完整用户信息
    const response = await getUserById(userId)
    return response.data
  } catch (error) {
    console.error('获取当前用户信息失败:', error)
    return null
  }
}
