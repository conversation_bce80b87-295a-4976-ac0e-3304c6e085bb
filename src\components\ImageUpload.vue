<template>
  <div class="image-upload">
    <div class="upload-area" @click="triggerFileInput">
      <!-- 预览区域 -->
      <div v-if="previewUrl" class="preview-container">
        <img :src="previewUrl" alt="预览图片" class="preview-image" />
        <div class="preview-overlay">
          <el-icon class="preview-icon"><Camera /></el-icon>
          <span class="preview-text">点击更换图片</span>
        </div>
        <el-button
          type="danger"
          :icon="Delete"
          circle
          size="small"
          class="delete-btn"
          @click.stop="clearImage"
        />
      </div>

      <!-- 上传区域 -->
      <div v-else class="upload-placeholder">
        <el-icon class="upload-icon"><Plus /></el-icon>
        <div class="upload-text">
          <p>点击上传图片</p>
          <p class="upload-hint">支持 JPG、PNG、GIF、WebP 格式，大小不超过 2MB</p>
        </div>
      </div>
    </div>

    <!-- 隐藏的文件输入 -->
    <input
      ref="fileInput"
      type="file"
      accept="image/*"
      @change="handleFileChange"
      style="display: none"
    />

    <!-- 进度条 -->
    <el-progress
      v-if="uploading"
      :percentage="uploadProgress"
      :show-text="false"
      class="upload-progress"
    />

    <!-- 图片信息 -->
    <div v-if="imageInfo" class="image-info">
      <div class="info-item">
        <span class="info-label">尺寸：</span>
        <span>{{ imageInfo.width }} × {{ imageInfo.height }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">大小：</span>
        <span>{{ formatFileSize(imageInfo.size) }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">格式：</span>
        <span>{{ imageInfo.type }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Camera, Delete } from '@element-plus/icons-vue'
import {
  validateImageFile,
  compressImage,
  getImageDimensions,
  isValidBase64Image,
  getImageTypeFromBase64,
  getBase64Size
} from '@/utils/image'

interface Props {
  modelValue?: string // base64字符串
  maxWidth?: number
  maxHeight?: number
  quality?: number
  compress?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'change', value: string): void
  (e: 'error', message: string): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  maxWidth: 800,
  maxHeight: 800,
  quality: 0.8,
  compress: true
})

const emit = defineEmits<Emits>()

const fileInput = ref<HTMLInputElement>()
const previewUrl = ref('')
const uploading = ref(false)
const uploadProgress = ref(0)
const imageInfo = ref<{
  width: number
  height: number
  size: number
  type: string
} | null>(null)

// 更新图片信息
const updateImageInfo = async (base64: string) => {
  try {
    const img = new Image()
    img.onload = () => {
      imageInfo.value = {
        width: img.width,
        height: img.height,
        size: getBase64Size(base64),
        type: getImageTypeFromBase64(base64)?.toUpperCase() || 'UNKNOWN'
      }
    }
    img.src = base64
  } catch (error) {
    console.error('获取图片信息失败:', error)
  }
}

// 监听modelValue变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue && isValidBase64Image(newValue)) {
      previewUrl.value = newValue
      updateImageInfo(newValue)
    } else {
      previewUrl.value = ''
      imageInfo.value = null
    }
  },
  { immediate: true }
)

// 触发文件选择
const triggerFileInput = () => {
  fileInput.value?.click()
}

// 处理文件选择
const handleFileChange = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (!file) return

  // 验证文件
  const validation = validateImageFile(file)
  if (!validation.valid) {
    ElMessage.error(validation.message!)
    emit('error', validation.message!)
    return
  }

  try {
    uploading.value = true
    uploadProgress.value = 0

    // 模拟上传进度
    const progressInterval = setInterval(() => {
      if (uploadProgress.value < 90) {
        uploadProgress.value += 10
      }
    }, 100)

    let base64: string

    if (props.compress) {
      // 压缩图片
      base64 = await compressImage(file, props.maxWidth, props.maxHeight, props.quality)
    } else {
      // 直接转换为base64
      const { fileToBase64 } = await import('@/utils/image')
      base64 = await fileToBase64(file)
    }

    clearInterval(progressInterval)
    uploadProgress.value = 100

    // 更新预览和数据
    previewUrl.value = base64
    updateImageInfo(base64)

    emit('update:modelValue', base64)
    emit('change', base64)

    ElMessage.success('图片上传成功')
  } catch (error) {
    console.error('图片处理失败:', error)
    const message = '图片处理失败，请重试'
    ElMessage.error(message)
    emit('error', message)
  } finally {
    uploading.value = false
    uploadProgress.value = 0
    // 清空input值，允许重复选择同一文件
    if (target) target.value = ''
  }
}

// 清除图片
const clearImage = () => {
  previewUrl.value = ''
  imageInfo.value = null
  emit('update:modelValue', '')
  emit('change', '')
}



// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
</script>

<style scoped>
.image-upload {
  width: 100%;
}

.upload-area {
  position: relative;
  width: 150px;
  height: 150px;
  border: 2px dashed #dcdfe6;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
}

.upload-area:hover {
  border-color: #409eff;
}

.preview-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.preview-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  color: white;
}

.upload-area:hover .preview-overlay {
  opacity: 1;
}

.preview-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.preview-text {
  font-size: 12px;
}

.delete-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 10;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #8c939d;
}

.upload-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

.upload-text {
  text-align: center;
}

.upload-text p {
  margin: 0;
  font-size: 14px;
}

.upload-hint {
  font-size: 12px;
  color: #a8abb2;
  margin-top: 4px;
}

.upload-progress {
  margin-top: 12px;
}

.image-info {
  margin-top: 12px;
  padding: 8px;
  background: #f5f7fa;
  border-radius: 4px;
  font-size: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  color: #606266;
  font-weight: 500;
}
</style>
