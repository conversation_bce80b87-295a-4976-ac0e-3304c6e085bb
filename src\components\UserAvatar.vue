<template>
  <el-avatar
    :size="size"
    :src="avatarSrc"
    :icon="fallbackIcon"
    :class="avatarClass"
    v-bind="$attrs"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { User as UserIcon } from '@element-plus/icons-vue'
import { getAvatarSrc, generateDefaultAvatar } from '@/utils/image'

interface Props {
  avatar?: string
  username?: string
  size?: number | string
  fallbackIcon?: any
  useGeneratedAvatar?: boolean
  class?: string
}

const props = withDefaults(defineProps<Props>(), {
  avatar: '',
  username: '',
  size: 40,
  fallbackIcon: UserIcon,
  useGeneratedAvatar: true,
  class: ''
})

// 计算头像源
const avatarSrc = computed(() => {
  const validAvatar = getAvatarSrc(props.avatar)
  
  if (validAvatar) {
    return validAvatar
  }
  
  // 如果没有有效头像且启用生成默认头像
  if (props.useGeneratedAvatar && props.username) {
    const size = typeof props.size === 'number' ? props.size : parseInt(props.size.toString())
    return generateDefaultAvatar(props.username, size)
  }
  
  // 返回undefined，让el-avatar使用fallbackIcon
  return undefined
})

// 头像样式类
const avatarClass = computed(() => {
  return ['user-avatar', props.class].filter(Boolean).join(' ')
})
</script>

<style scoped>
.user-avatar {
  cursor: pointer;
  transition: all 0.3s ease;
}

.user-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
</style>
