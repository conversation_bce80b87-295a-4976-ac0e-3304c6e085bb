<script setup lang="ts">
import { RouterView, useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import { SwitchButton } from '@element-plus/icons-vue'
import UserAvatar from '@/components/UserAvatar.vue'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()
const active = ref('')

active.value = route.meta.menuIndex as string

// 退出登录
const logout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    userStore.clearUser()
    ElMessage.success('已退出登录')
    router.push('/login')
  } catch {
    // 用户取消操作
  }
}

// 页面加载时获取用户信息
onMounted(async () => {
  await userStore.fetchCurrentUser()
  console.log('用户信息:', userStore.currentUser)
  console.log('是否为管理员:', userStore.isAdmin)
})
</script>

<template>
  <div class="app-container">
    <el-container>
      <el-header class="header-container">
        <div class="header-left">
          <el-menu
            mode="horizontal"
            :router="true"
            :default-active="active"
            class="main-menu"
            v-if="!userStore.loading"
            :ellipsis="false"
          >
            <el-menu-item index="/home">首页</el-menu-item>
            <el-menu-item index="/repos">开源项目</el-menu-item>
            <el-menu-item index="/news">开源新闻</el-menu-item>
            <el-menu-item index="/contributors">开源贡献者</el-menu-item>
            <el-menu-item index="/about">关于</el-menu-item>
            <el-menu-item
              v-if="userStore.currentUser && userStore.currentUser.type === 1"
              index="/audit"
            >
              审核
            </el-menu-item>
          </el-menu>
          <div v-else class="menu-loading">
            <el-skeleton :rows="1" animated />
          </div>
        </div>

        <div class="header-right">
          <!-- 临时调试信息 -->
          <div v-if="userStore.currentUser" class="debug-info" style="margin-right: 20px; font-size: 12px; color: #666;">
            用户类型: {{ userStore.userTypeText }} |
            是否管理员: {{ userStore.isAdmin ? '是' : '否' }}
          </div>

          <div v-if="userStore.currentUser" class="user-info">
            <UserAvatar
              :size="32"
              :avatar="userStore.currentUser.avatar"
              :username="userStore.currentUser.username"
              :use-generated-avatar="true"
            />
            <span class="username">{{ userStore.currentUser.nickname || userStore.currentUser.username }}</span>
            <el-button
              type="text"
              :icon="SwitchButton"
              @click="logout"
              class="logout-btn"
            >
              退出
            </el-button>
          </div>
          <div v-else-if="userStore.loading" class="loading-user">
            <el-skeleton :rows="1" animated />
          </div>
          <div v-else class="no-user">
            <span class="no-user-text">未登录</span>
          </div>
        </div>
      </el-header>
      <el-main>
        <RouterView />
      </el-main>
      <el-footer>
        <div class="footer-content">
          <p>&copy; 2025 齐鲁开源社. All rights reserved.</p>
          <p>
            <a href="/privacy-policy">隐私政策</a> |
            <a href="/terms-of-service">服务条款</a>
          </p>
        </div>
      </el-footer>
    </el-container>
  </div>
</template>

<style scoped>
.app-container {
  max-width: 1920px;
  min-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  width: 100%;
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  background-color: #fff;
  border-bottom: 1px solid #e4e7ed;
  min-width: 1160px; /* 确保header有足够宽度 */
}

.header-left {
  flex: 1;
  min-width: 600px; /* 确保有足够空间显示所有菜单项 */
}

.main-menu {
  border-bottom: none;
  width: 100%;
}

.menu-loading {
  height: 60px;
  display: flex;
  align-items: center;
  min-width: 400px;
}

.header-right {
  display: flex;
  align-items: center;
  min-width: 200px;
  justify-content: flex-end;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  cursor: pointer;
}

.username {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

.logout-btn {
  color: #909399;
  font-size: 14px;
}

.logout-btn:hover {
  color: #409eff;
}

.loading-user {
  width: 150px;
}

.no-user {
  display: flex;
  align-items: center;
}

.no-user-text {
  color: #909399;
  font-size: 14px;
}

.footer-content {
  text-align: center;
  padding: 20px 0;
  background-color: #f8f8f8;
  border-top: 1px solid #e7e7e7;
}

.footer-content p {
  margin: 5px 0;
}

.footer-content a {
  color: #409eff;
  text-decoration: none;
}

.footer-content a:hover {
  text-decoration: underline;
}
</style>
