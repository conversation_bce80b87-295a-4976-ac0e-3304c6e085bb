import { createRouter, createWebHistory } from 'vue-router'
import AppLayout from '@/layouts/AppLayout.vue'
import AuthLayout from '@/layouts/AuthLayout.vue'
import { hasToken, isTokenExpired } from '@/utils/auth'
import { useUserStore } from '@/stores/user'
import '@/types/router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      component: AppLayout,
      redirect: '/home',
      meta: { requiresAuth: true },
      children: [
        {
          path: '/home',
          name: 'home',
          component: () => import('../views/HomeView.vue'),
          meta: {
            menuIndex: '/home',
          },
        },
        {
          path: '/repos',
          name: 'repos',
          component: () => import('../views/ReposView.vue'),
          meta: {
            menuIndex: '/repos',
          },
        },
        {
          path: '/repo/:id',
          name: 'repo',
          component: () => import('../views/RepoDetailView.vue'),
          props: true,
          meta: {
            menuIndex: '/repos',
          },
        },
        {
          path: '/news',
          name: 'news',
          component: () => import('../views/NewsView.vue'),
          meta: {
            menuIndex: '/news',
          },
        },
        {
          path: '/contributors',
          name: 'contributors',
          component: () => import('../views/ContributorsView.vue'),
          meta: {
            menuIndex: '/contributors',
          },
        },
        {
          path: '/about',
          name: 'about',
          component: () => import('../views/AboutView.vue'),
          meta: {
            menuIndex: '/about',
          },
        },
        {
          path: '/audit',
          name: 'audit',
          component: () => import('../views/AuditView.vue'),
          meta: {
            menuIndex: '/audit',
            requiresAdmin: true,
          },
        },
      ],
    },

    {
      path: '/login',
      component: AuthLayout,
      meta: { requiresAuth: false },
      children: [
        {
          path: '',
          name: 'login',
          component: () => import('../views/LoginView.vue'),
        },
      ],
    },

    {
      path: '/test',
      name: 'test',
      component: () => import('../views/TestView.vue'),
    },
  ],
})

// 路由守卫
router.beforeEach(async (to, _from, next) => {
  const requiresAuth = to.meta.requiresAuth
  const requiresAdmin = to.meta.requiresAdmin
  const token = hasToken()
  const tokenExpired = isTokenExpired()

  if (requiresAuth) {
    if (!token || tokenExpired) {
      // 需要认证但没有token或token已过期，跳转到登录页
      next({
        name: 'login',
        query: { redirect: to.fullPath }
      })
      return
    }

    // 如果需要admin权限
    if (requiresAdmin) {
      const userStore = useUserStore()

      // 如果用户信息还没有加载，先加载用户信息
      if (!userStore.currentUser) {
        await userStore.fetchCurrentUser()
      }

      // 检查是否为admin用户
      if (!userStore.isAdmin) {
        // 非admin用户访问admin页面，重定向到首页
        next({ name: 'home' })
        return
      }
    }

    next()
  } else {
    // 不需要认证的页面
    if (to.name === 'login' && token && !tokenExpired) {
      // 已登录用户访问登录页，重定向到首页
      next({ name: 'home' })
    } else {
      next()
    }
  }
})

export default router
