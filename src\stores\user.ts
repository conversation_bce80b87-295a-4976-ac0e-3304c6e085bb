import { ref } from 'vue'
import { defineStore } from 'pinia'
import { getCurrentUser } from '@/apis/user'
import { removeToken, hasToken } from '@/utils/auth'
import type { User } from '@/types/user'

export const useUserStore = defineStore('user', () => {
  const currentUser = ref<User | null>(null)
  const loading = ref(false)

  // 获取当前用户信息
  const fetchCurrentUser = async () => {
    if (!hasToken()) {
      currentUser.value = null
      return null
    }

    loading.value = true
    try {
      const user = await getCurrentUser()
      currentUser.value = user
      return user
    } catch (error) {
      console.error('获取用户信息失败:', error)
      currentUser.value = null
      return null
    } finally {
      loading.value = false
    }
  }

  // 清除用户信息
  const clearUser = () => {
    currentUser.value = null
    removeToken()
  }

  // 更新用户信息
  const updateCurrentUser = (user: User) => {
    currentUser.value = user
  }

  // 检查是否已登录
  const isLoggedIn = computed(() => {
    return !!currentUser.value && hasToken()
  })

  // 检查是否为管理员
  const isAdmin = computed(() => {
    // 后端返回的type字段：0=普通用户，1=管理员
    return currentUser.value?.type === 1
  })

  // 获取用户类型文本描述
  const userTypeText = computed(() => {
    if (!currentUser.value) return '未登录'
    switch (currentUser.value.type) {
      case 0: return '普通用户'
      case 1: return '管理员'
      default: return '未设置'
    }
  })

  return {
    currentUser: readonly(currentUser),
    loading: readonly(loading),
    isLoggedIn,
    isAdmin,
    userTypeText,
    fetchCurrentUser,
    clearUser,
    updateCurrentUser
  }
})
