type NewsListReqParams = {
  news?: {
    id?: number
    writer?: string
    star?: number
    likesNum?: number
    topic?: string
    url?: string
    publishTime?: string
    collectionTime?: string
  }
  pageNumber?: number
  pageSize?: number
  sortField?: string
  sortOrder?: string
}

type NewsListResp = {
  current: number
  total: number
  records: {
    id: number
    url: string
    writer: string
    star: number
    likesNum: number
    topic: string
    publishTime: string
    collectTime: string
    source: string | null
    introduction: string | null
    tagList: {
      id: number | null
      name: string
      userId: number | null
      isPublic: boolean | null
      createAt: string | null
    }[]
  }[]
  size: number
  pages: number
}

export type { NewsListReqParams, NewsListResp }
