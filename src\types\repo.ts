export type RepoListReqParams = {
  repository?: {
    id?: number
    githubId?: number
    name?: string
    htmlUrl?: string
    description?: string
    collectionTime?: string
    tagString?: string
    defaultBranch?: string
    organizationId?: number
    readmeContent?: string
    readmeSha?: string
    watch?: number
    star?: number
    fork?: number
    recentIssues?: number
    recentCommits?: number
    recentPr?: number
    evaluationBasic?: string
  }
  tag?: { name: string }
  pageNumber?: number
  pageSize?: number
  beginTime?: number
  endTime?: number
  sortField?: string
  sortOrder?: string
}

export type RepoTagsReqParams = {
  tag: { name: string }
  pageNumber: number
  pageSize: number
  sortOrder: string
}

export type RepoListResp = {
  current: number
  total: number
  records: RepoListItem[]
  size: number
  pages: number
}

export type RepoListItem = {
  id: number
  name: string
  htmlUrl: string
  description: string
  collectionTime: string
  watch: number
  star: number
  fork: number
  recentCommits: number
}

export type RepoDetail = {
  /**
   * 仓库首页url，api调用
   */
  apiUrl?: string;
  /**
   * 最新收集时间和生成 basic 评价的时间
   */
  collectionTime?: Date;
  /**
   * 默认分支
   */
  defaultBranch?: string;
  /**
   * 项目描述、简介
   */
  description?: string;
  /**
   * 项目元数据生成的 basic 评价，更新取决于 SHA 是否变化
   */
  evaluationBasic?: string;
  /**
   * 项目的 Fork 数量
   */
  fork?: number;
  /**
   * github项目唯一id
   */
  githubId?: number;
  /**
   * 仓库首页url
   */
  htmlUrl?: string;
  /**
   * 项目 ID
   */
  id?: number;
  /**
   * 项目名称
   */
  name?: string;
  /**
   * 所属组织
   */
  organizationId?: number;
  /**
   * README 内容
   */
  readmeContent?: string;
  /**
   * README 文件的 SHA 值
   */
  readmeSha?: string;
  /**
   * 近半年 commits 量
   */
  recentCommits?: number;
  /**
   * 近半年 issues 量
   */
  recentIssues?: number;
  /**
   * 近半年 PR 量
   */
  recentPr?: number;
  /**
   * 项目的 Star 数量
   */
  star?: number;
  /**
   * 项目自带标签字符串
   */
  tagString?: string;
  /**
   * 项目的 Watch 数量
   */
  watch?: number;
}
