// 用户信息类型定义
export type User = {
  id: number
  username: string
  password?: string
  signature?: string
  type?: number  // 修改为number类型：0=普通用户，1=管理员
  avatar?: string
  nickname?: string
  createTime?: string
  updateTime?: string
}

// 获取用户信息请求参数
export type GetUserByIdReq = {
  id: number
}

export type GetUserByUsernameReq = {
  username: string
}

// 更新用户信息请求参数
export type UpdateUserReq = {
  userId: number
  user: Partial<User>
}

// 更新用户头像请求参数
export type UpdateUserAvatarReq = {
  userId: number
  avatar: string
}

// 重置用户密码请求参数
export type ResetUserPasswordReq = {
  userId: number
  oldPwd: string
  newPwd: string
}

// 用户信息响应类型
export type UserResp = User

// 当前登录用户信息（从token解析或session获取）
export type CurrentUser = {
  id: number
  username: string
  nickname?: string
  avatar?: string
  signature?: string
  type?: number  // 修改为number类型：0=普通用户，1=管理员
}
