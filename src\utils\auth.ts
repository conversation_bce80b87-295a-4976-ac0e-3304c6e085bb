const saveToken = (token: string) => {
  localStorage.setItem('token', token)
}

const getToken = () => {
  return localStorage.getItem('token')
}

const removeToken = () => {
  localStorage.removeItem('token')
}

const hasToken = () => {
  return !!getToken()
}

// 简单的JWT解析函数（仅解析payload，不验证签名）
const parseJWT = (token: string) => {
  try {
    const base64Url = token.split('.')[1]
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/')
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(function (c) {
          return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)
        })
        .join('')
    )
    return JSON.parse(jsonPayload)
  } catch (error) {
    console.error('JWT解析失败:', error)
    return null
  }
}

// 从token中获取用户ID
const getUserIdFromToken = (): number | null => {
  const token = getToken()
  if (!token) return null

  const payload = parseJWT(token)
  if (!payload) return null

  // 根据后端JWT的结构来获取用户ID
  // 常见的字段名：sub, userId, id, user_id
  return payload.sub || payload.userId || payload.id || payload.user_id || null
}

// 检查token是否过期
const isTokenExpired = (): boolean => {
  const token = getToken()
  if (!token) return true

  const payload = parseJWT(token)
  if (!payload || !payload.exp) return true

  const currentTime = Math.floor(Date.now() / 1000)
  return payload.exp < currentTime
}

export { saveToken, getToken, removeToken, hasToken, parseJWT, getUserIdFromToken, isTokenExpired }
