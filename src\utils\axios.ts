import router from '@/router'
import axios from 'axios'
import qs from 'qs'
import { getToken } from './auth'
import { ElMessage } from 'element-plus'

const api = axios.create({
  baseURL: '/api',
  headers: {
    'Content-Type': 'application/json',
  },
  paramsSerializer: (params) => {
    return qs.stringify(params, { allowDots: true })
  },
})

api.interceptors.request.use(
  (config) => {
    if (router.currentRoute.value.meta.requiresAuth) {
      const token = getToken()
      if (token) {
        // if token exists, set Authorization header
        config.headers.Authorization = `Bearer ${token}`
      } else {
        // if token doesn't exist, go to login page and redirect back after login
        router.push({ name: 'login', query: { redirect: router.currentRoute.value.path } })
      }
    }
    return config
  },
  (error) => {
    // Do something with request error
    return Promise.reject(error)
  },
)

api.interceptors.response.use(
  (response) => {
    // status code 2xx
    const data = response.data
    if (data.success === false) {
      ElMessage.error(data.msg)
      return Promise.reject(data)
    }
    return response.data
  },
  (error) => {
    // status code isn't 2xx

    // if the status code is 401, it means the token is expired
    if (error.status === 401) {
      // if the token is expired, go to login page and redirect back after login
      ElMessage.error('登录状态已过期，请重新登录')
      router.push({ name: 'login', query: { redirect: router.currentRoute.value.fullPath } })
      return null
    }
    return Promise.reject(error)
  },
)

export default api
