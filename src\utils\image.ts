// 图片处理工具函数

// 支持的图片格式
export const SUPPORTED_IMAGE_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']

// 最大文件大小 (2MB)
export const MAX_FILE_SIZE = 2 * 1024 * 1024

// 验证图片文件
export const validateImageFile = (file: File): { valid: boolean; message?: string } => {
  // 检查文件类型
  if (!SUPPORTED_IMAGE_TYPES.includes(file.type)) {
    return {
      valid: false,
      message: '不支持的图片格式，请选择 JPG、PNG、GIF 或 WebP 格式的图片'
    }
  }

  // 检查文件大小
  if (file.size > MAX_FILE_SIZE) {
    return {
      valid: false,
      message: `图片大小不能超过 ${MAX_FILE_SIZE / 1024 / 1024}MB`
    }
  }

  return { valid: true }
}

// 将文件转换为base64
export const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => {
      if (typeof reader.result === 'string') {
        resolve(reader.result)
      } else {
        reject(new Error('读取文件失败'))
      }
    }
    reader.onerror = () => reject(new Error('读取文件失败'))
    reader.readAsDataURL(file)
  })
}

// 压缩图片
export const compressImage = (
  file: File,
  maxWidth: number = 800,
  maxHeight: number = 800,
  quality: number = 0.8
): Promise<string> => {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()

    img.onload = () => {
      // 计算压缩后的尺寸
      let { width, height } = img

      if (width > maxWidth || height > maxHeight) {
        const ratio = Math.min(maxWidth / width, maxHeight / height)
        width *= ratio
        height *= ratio
      }

      canvas.width = width
      canvas.height = height

      // 绘制压缩后的图片
      ctx?.drawImage(img, 0, 0, width, height)

      // 转换为base64
      const compressedBase64 = canvas.toDataURL(file.type, quality)
      resolve(compressedBase64)
    }

    img.onerror = () => reject(new Error('图片加载失败'))

    // 读取文件并设置图片源
    const reader = new FileReader()
    reader.onload = (e) => {
      img.src = e.target?.result as string
    }
    reader.onerror = () => reject(new Error('读取文件失败'))
    reader.readAsDataURL(file)
  })
}

// 获取图片尺寸
export const getImageDimensions = (file: File): Promise<{ width: number; height: number }> => {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.onload = () => {
      resolve({ width: img.width, height: img.height })
    }
    img.onerror = () => reject(new Error('无法获取图片尺寸'))

    const reader = new FileReader()
    reader.onload = (e) => {
      img.src = e.target?.result as string
    }
    reader.onerror = () => reject(new Error('读取文件失败'))
    reader.readAsDataURL(file)
  })
}

// 检查base64字符串是否为有效图片
export const isValidBase64Image = (base64: string): boolean => {
  if (!base64 || typeof base64 !== 'string') return false

  // 检查是否以data:image开头
  const imagePattern = /^data:image\/(jpeg|jpg|png|gif|webp);base64,/
  return imagePattern.test(base64)
}

// 从base64字符串中提取图片类型
export const getImageTypeFromBase64 = (base64: string): string | null => {
  const match = base64.match(/^data:image\/([a-zA-Z]+);base64,/)
  return match ? match[1] : null
}

// 计算base64字符串的大小（字节）
export const getBase64Size = (base64: string): number => {
  if (!base64) return 0

  // 移除data:image/...;base64,前缀
  const base64Data = base64.split(',')[1] || base64

  // base64编码后的大小约为原始大小的4/3
  return Math.round((base64Data.length * 3) / 4)
}

// 处理头像显示
export const getAvatarSrc = (avatar?: string): string | undefined => {
  if (!avatar) return undefined

  // 如果是base64格式，直接返回
  if (isValidBase64Image(avatar)) {
    return avatar
  }

  // 如果是URL格式，检查是否有效
  try {
    new URL(avatar)
    return avatar
  } catch {
    // 无效的URL，返回undefined使用默认头像
    return undefined
  }
}

// 生成默认头像（使用用户名首字母）
export const generateDefaultAvatar = (username: string, size: number = 100): string => {
  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')

  canvas.width = size
  canvas.height = size

  if (!ctx) return ''

  // 背景色（根据用户名生成）
  const colors = [
    '#f56565', '#ed8936', '#ecc94b', '#48bb78', '#38b2ac',
    '#4299e1', '#667eea', '#9f7aea', '#ed64a6', '#a0aec0'
  ]
  const colorIndex = username.charCodeAt(0) % colors.length
  ctx.fillStyle = colors[colorIndex]
  ctx.fillRect(0, 0, size, size)

  // 文字
  const firstLetter = username.charAt(0).toUpperCase()
  ctx.fillStyle = '#ffffff'
  ctx.font = `bold ${size * 0.4}px Arial`
  ctx.textAlign = 'center'
  ctx.textBaseline = 'middle'
  ctx.fillText(firstLetter, size / 2, size / 2)

  return canvas.toDataURL('image/png')
}
