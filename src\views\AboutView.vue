<script setup lang="ts">
import { Location, Message, Link, View, Star, Phone, Aim, Check } from '@element-plus/icons-vue'

// 静态数据
const teamInfo = {
  mission:
    '齐鲁开源社致力于推动开源技术在齐鲁大地的发展与创新，培养和支持本土开源人才，促进开源文化的传播与实践。',
  vision: '打造齐鲁地区最具影响力的开源技术社区，成为连接全球开源世界与本土技术人才的桥梁。',
  values: [
    '开放协作：鼓励知识共享和团队协作',
    '创新驱动：推动技术创新和实践应用',
    '本土发展：扎根齐鲁，服务本地技术生态',
    '全球视野：对接国际开源社区，拓展技术视野',
  ],
  contact: {
    email: '<EMAIL>',
    address: '山东省济南市高新区',
    website: 'www.qiluopensource.org',
  },
}
</script>

<template>
  <div class="about-container">
    <div class="hero-section">
      <img src="/齐鲁开源社logo.png" alt="齐鲁开源社 Logo" class="logo" />
      <div class="hero-overlay">
        <h1 class="hero-title">齐鲁开源社</h1>
        <p class="hero-subtitle">连接开源世界 · 助力技术创新</p>
      </div>
    </div>

    <div class="content-section">
      <el-card class="section-card mission-card">
        <template #header>
          <div class="card-header">
            <el-icon class="header-icon"><Aim /></el-icon>
            <h2>使命</h2>
          </div>
        </template>
        <div class="card-content">
          <p>{{ teamInfo.mission }}</p>
        </div>
      </el-card>

      <el-card class="section-card vision-card">
        <template #header>
          <div class="card-header">
            <el-icon class="header-icon"><View /></el-icon>
            <h2>愿景</h2>
          </div>
        </template>
        <div class="card-content">
          <p>{{ teamInfo.vision }}</p>
        </div>
      </el-card>

      <el-card class="section-card values-card">
        <template #header>
          <div class="card-header">
            <el-icon class="header-icon"><Star /></el-icon>
            <h2>核心价值观</h2>
          </div>
        </template>
        <div class="card-content values-grid">
          <div v-for="(value, index) in teamInfo.values" :key="index" class="value-item">
            <div class="value-icon">
              <el-icon><Check /></el-icon>
            </div>
            <p>{{ value }}</p>
          </div>
        </div>
      </el-card>

      <el-card class="section-card contact-card">
        <template #header>
          <div class="card-header">
            <el-icon class="header-icon"><Phone /></el-icon>
            <h2>联系我们</h2>
          </div>
        </template>
        <div class="card-content contact-grid">
          <div class="contact-item">
            <el-icon><Location /></el-icon>
            <div class="contact-info">
              <h4>地址</h4>
              <p>{{ teamInfo.contact.address }}</p>
            </div>
          </div>
          <div class="contact-item">
            <el-icon><Message /></el-icon>
            <div class="contact-info">
              <h4>邮箱</h4>
              <p>{{ teamInfo.contact.email }}</p>
            </div>
          </div>
          <div class="contact-item">
            <el-icon><Link /></el-icon>
            <div class="contact-info">
              <h4>网站</h4>
              <p>{{ teamInfo.contact.website }}</p>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<style scoped lang="scss">
.about-container {
  .hero-section {
    position: relative;
    width: 100%;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    margin-bottom: 40px;
    padding: 0;

    .logo {
      width: 100%;
      object-fit: contain;
      padding: 0;
      margin-top: -20px; // 让logo贴近导航栏
    }

    .hero-overlay {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      padding: 30px;
      background: linear-gradient(
        to top,
        rgba(0, 0, 0, 0.8) 0%,
        rgba(0, 0, 0, 0.4) 50%,
        transparent 100%
      );
      color: white;
      text-align: center;

      .hero-title {
        font-size: 3.5rem;
        margin: 0;
        font-weight: bold;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
      }

      .hero-subtitle {
        font-size: 1.8rem;
        margin: 10px 0 0;
        opacity: 0.9;
      }
    }
  }

  .content-section {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;

    .section-card {
      margin-bottom: 40px;
      border-radius: 12px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      transition:
        transform 0.3s ease,
        box-shadow 0.3s ease;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
      }

      .card-header {
        display: flex;
        align-items: center;
        padding: 20px;
        background: linear-gradient(135deg, #409eff, #2c5282);
        color: white;

        .header-icon {
          font-size: 24px;
          margin-right: 12px;
        }

        h2 {
          margin: 0;
          font-size: 24px;
        }
      }

      .card-content {
        padding: 24px;
        font-size: 16px;
        line-height: 1.6;
      }
    }

    .values-grid {
      display: grid;
      gap: 20px;

      .value-item {
        display: flex;
        align-items: flex-start;
        padding: 15px;
        background: #f8fafc;
        border-radius: 8px;

        .value-icon {
          background: #409eff;
          color: white;
          width: 32px;
          height: 32px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 15px;
          flex-shrink: 0;
        }

        p {
          margin: 0;
        }
      }
    }

    .contact-grid {
      display: grid;
      gap: 24px;

      .contact-item {
        display: flex;
        align-items: flex-start;
        padding: 15px;
        background: #f8fafc;
        border-radius: 8px;

        .el-icon {
          font-size: 24px;
          color: #409eff;
          margin-right: 15px;
        }

        .contact-info {
          h4 {
            margin: 0 0 5px;
            color: #2c5282;
          }

          p {
            margin: 0;
            color: #4a5568;
          }
        }
      }
    }
  }
}
</style>
