<template>
  <div class="audit-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>标签审核管理</span>
          <el-button type="primary" @click="fetchTags" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>

      <el-table
        :data="tags"
        style="width: 100%"
        v-loading="loading"
        empty-text="暂无待审核的标签"
      >
        <el-table-column prop="repoName" label="仓库名" min-width="200">
          <template #default="{ row }">
            <el-link type="primary" :href="`/repo/${row.repoId}`" target="_blank">
              {{ row.repoName }}
            </el-link>
          </template>
        </el-table-column>

        <el-table-column prop="tagName" label="标签名" min-width="150">
          <template #default="{ row }">
            <el-tag type="primary">{{ row.tagName }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="username" label="申请用户" min-width="120">
          <template #default="{ row }">
            <el-avatar :size="24" class="user-avatar">
              {{ row.username.charAt(0).toUpperCase() }}
            </el-avatar>
            <span class="username">{{ row.username }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="success"
              size="small"
              @click="approve(row)"
              :loading="row.approving"
            >
              <el-icon><Check /></el-icon>
              通过
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="reject(row)"
              :loading="row.rejecting"
            >
              <el-icon><Close /></el-icon>
              拒绝
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, Check, Close } from '@element-plus/icons-vue'
import { getPendingTags, approveTag, rejectTag } from '@/apis/tag'
import type { PendingTag } from '@/types/tag'

const tags = ref<PendingTag[]>([])
const loading = ref(false)

// 获取待审核标签列表
const fetchTags = async () => {
  loading.value = true
  try {
    const res = await getPendingTags()
    if (res.code === 0 || res.code === 200) {
      tags.value = res.data
    } else {
      ElMessage.error(res.msg || '获取标签失败')
    }
  } catch (e) {
    ElMessage.error('网络错误')
  } finally {
    loading.value = false
  }
}

// 审核通过
const approve = async (row: PendingTag) => {
  row.approving = true
  try {
    const repoId = row.repoId
    const tagId = String(row.tagId)
    const res = await approveTag({ repoId, tagId })
    if (res.code === 200 || res.code === 0) {
      ElMessage.success('审核通过')
      fetchTags() // 刷新列表
    } else {
      ElMessage.error(res.msg || '操作失败')
    }
  } catch (e: any) {
    ElMessage.error(e.message || '网络错误')
  } finally {
    row.approving = false
  }
}

// 审核拒绝
const reject = async (row: PendingTag) => {
  row.rejecting = true
  try {
    const repoId = row.repoId
    const tagId = String(row.tagId)
    const res = await rejectTag({ repoId, tagId })
    if (res.code === 200 || res.code === 0) {
      ElMessage.success('审核拒绝')
      fetchTags() // 刷新列表
    } else {
      ElMessage.error(res.msg || '操作失败')
    }
  } catch (e: any) {
    ElMessage.error(e.message || '网络错误')
  } finally {
    row.rejecting = false
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchTags()
})
</script>

<style scoped>
.audit-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-avatar {
  margin-right: 8px;
  background-color: #409eff;
  color: white;
  font-size: 12px;
}

.username {
  font-size: 14px;
  color: #606266;
}

:deep(.el-table) {
  border-radius: 8px;
}

:deep(.el-table th) {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 600;
}

:deep(.el-table td) {
  padding: 12px 0;
}

:deep(.el-button) {
  margin-right: 8px;
}

:deep(.el-button:last-child) {
  margin-right: 0;
}
</style>
