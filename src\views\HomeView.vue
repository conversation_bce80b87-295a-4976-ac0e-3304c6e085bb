<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { updateUser } from '@/apis/user'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import { Edit, User as UserIcon } from '@element-plus/icons-vue'
import ImageUpload from '@/components/ImageUpload.vue'
import UserAvatar from '@/components/UserAvatar.vue'

const userStore = useUserStore()
const editDialogVisible = ref(false)
const editForm = ref({
  nickname: '',
  signature: '',
  avatar: ''
})

// 打开编辑对话框
const openEditDialog = () => {
  if (userStore.currentUser) {
    editForm.value = {
      nickname: userStore.currentUser.nickname || '',
      signature: userStore.currentUser.signature || '',
      avatar: userStore.currentUser.avatar || ''
    }
    editDialogVisible.value = true
  }
}

// 保存用户信息
const saveUserInfo = async () => {
  if (!userStore.currentUser) return

  try {
    await updateUser(userStore.currentUser.id, {
      nickname: editForm.value.nickname,
      signature: editForm.value.signature,
      avatar: editForm.value.avatar
    })

    ElMessage.success('用户信息更新成功')
    editDialogVisible.value = false
    await userStore.fetchCurrentUser() // 重新获取用户信息
  } catch (error) {
    console.error('更新用户信息失败:', error)
    ElMessage.error('更新用户信息失败')
  }
}

// 处理图片上传错误
const handleUploadError = (message: string) => {
  console.error('图片上传错误:', message)
}

// 页面加载时获取用户信息
onMounted(() => {
  userStore.fetchCurrentUser()
})
</script>

<template>
  <main class="home-container">
    <div class="welcome-section">
      <h1 class="page-title">欢迎来到齐鲁开源社</h1>
      <p class="page-subtitle">探索开源世界，分享技术创新</p>
    </div>

    <!-- 用户信息卡片 -->
    <el-card class="user-card" v-loading="userStore.loading">
      <template #header>
        <div class="card-header">
          <span class="card-title">
            <el-icon><UserIcon /></el-icon>
            个人信息
          </span>
          <el-button
            type="primary"
            :icon="Edit"
            size="small"
            @click="openEditDialog"
            v-if="userStore.currentUser"
          >
            编辑
          </el-button>
        </div>
      </template>

      <div v-if="userStore.currentUser" class="user-info-content">
        <div class="user-avatar-section">
          <UserAvatar
            :size="80"
            :avatar="userStore.currentUser.avatar"
            :username="userStore.currentUser.username"
            :use-generated-avatar="true"
            class="large-avatar"
          />
        </div>

        <div class="user-details">
          <div class="info-item">
            <label>用户名：</label>
            <span>{{ userStore.currentUser.username }}</span>
          </div>

          <div class="info-item">
            <label>昵称：</label>
            <span>{{ userStore.currentUser.nickname || '未设置' }}</span>
          </div>

          <div class="info-item">
            <label>个性签名：</label>
            <span>{{ userStore.currentUser.signature || '这个人很懒，什么都没留下' }}</span>
          </div>

          <div class="info-item">
            <label>注册时间：</label>
            <span>{{ userStore.currentUser.createTime ? new Date(userStore.currentUser.createTime).toLocaleDateString() : '未知' }}</span>
          </div>
        </div>
      </div>

      <div v-else class="no-user">
        <el-empty description="无法获取用户信息" />
      </div>
    </el-card>

    <!-- 编辑用户信息对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑个人信息"
      width="600px"
      :before-close="() => editDialogVisible = false"
    >
      <el-form :model="editForm" label-width="80px">
        <el-form-item label="头像">
          <div class="avatar-upload-container">
            <ImageUpload
              v-model="editForm.avatar"
              :max-width="400"
              :max-height="400"
              :quality="0.8"
              :compress="true"
              @error="handleUploadError"
            />
            <div class="avatar-upload-tips">
              <div class="tips-content">
                <span>• 建议上传正方形图片，系统会自动压缩</span>
                <span>• 支持 JPG、PNG、GIF、WebP 格式</span>
                <span>• 文件大小不超过 2MB</span>
              </div>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="昵称">
          <el-input
            v-model="editForm.nickname"
            placeholder="请输入昵称"
            maxlength="20"
            show-word-limit
            clearable
          />
        </el-form-item>

        <el-form-item label="个性签名">
          <el-input
            v-model="editForm.signature"
            type="textarea"
            placeholder="请输入个性签名"
            maxlength="100"
            show-word-limit
            :rows="3"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveUserInfo">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </main>
</template>

<style scoped>
.home-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.welcome-section {
  text-align: center;
  margin-bottom: 30px;
  padding: 40px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 600;
  margin-bottom: 10px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  margin: 0;
}

.user-card {
  margin-bottom: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.user-info-content {
  display: flex;
  gap: 30px;
  align-items: flex-start;
}

.user-avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.large-avatar {
  border: 3px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.user-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f5f5f5;
}

.info-item:last-child {
  border-bottom: none;
}

.info-item label {
  font-weight: 600;
  color: #606266;
  min-width: 100px;
  margin-right: 12px;
}

.info-item span {
  color: #303133;
  flex: 1;
}

.no-user {
  text-align: center;
  padding: 40px 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.avatar-upload-container {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.avatar-upload-tips {
  flex: 1;
  display: flex;
  align-items: center;
  min-height: 60px;
  max-width: 350px;
  min-width: 250px;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: 3px;
  font-size: 12px;
  color: #909399;
  line-height: 1.2;
}

.tips-content span {
  display: block;
  white-space: nowrap;
}

@media (max-width: 768px) {
  .user-info-content {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .user-details {
    width: 100%;
  }

  .page-title {
    font-size: 2rem;
  }

  .page-subtitle {
    font-size: 1rem;
  }
}
</style>
