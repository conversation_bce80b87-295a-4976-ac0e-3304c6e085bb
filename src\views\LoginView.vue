<script setup lang="ts">
import { ref } from 'vue'
import { ElForm, ElFormItem } from 'element-plus'
import { useRouter } from 'vue-router'
import { loginAPI } from '@/apis/login'
import type { LoginReq } from '@/types/login'
import { saveToken } from '@/utils/auth'

const router = useRouter()

const title = ref('登录')

// 设置formRef类型为ElForm或null
const formRef = ref<InstanceType<typeof ElForm> | null>(null)
const username = ref('')
const password = ref('')

const login = async () => {
  if (formRef.value) {
    const valid = await formRef.value.validate()
    if (!valid) return
  }

  let resp;
  try {
    resp = await loginAPI({
      username: username.value,
      password: password.value,
    } as LoginReq)
  } catch {
    return
  }
  const { token } = resp.data
  if (token) {
    saveToken(token)
    if (router.currentRoute.value.query.redirect) {
      router.push({ path: router.currentRoute.value.query.redirect as string })
    } else {
      router.push({ path: '/' })
    }
  }
}

const register = () => {
  router.push('/register') // TODO: Add register page
}
</script>

<template>
  <div class="login-container">
    <el-card class="login-card">
      <h2 class="login-title">{{ title }}</h2>
      <el-form
        ref="formRef"
        :model="{ username, password }"
        label-width="auto"
        @keydown.enter="login"
      >
        <el-form-item
          label="用户名"
          prop="username"
          :rules="[{ required: true, message: '请输入用户名', trigger: 'blur' }]"
        >
          <el-input v-model="username" />
        </el-form-item>
        <el-form-item
          label="密码"
          prop="password"
          :rules="[{ required: true, message: '请输入密码', trigger: 'blur' }]"
        >
          <el-input type="password" show-password v-model="password" />
        </el-form-item>
        <div class="centered-div">
          <el-button type="primary" @click="login">登录</el-button>
          <el-button @click="register">注册</el-button>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  background-image: url('background.png');
  background-size: cover;
  background-position: center;
}

.login-card {
  width: 400px;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  background-color: rgba(255, 255, 255, 0.9);
}

.login-title {
  text-align: center;
  margin-bottom: 20px;
}

.centered-div {
  display: flex;
  justify-content: center;
}
</style>
