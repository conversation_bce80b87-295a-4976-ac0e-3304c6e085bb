<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { getNewsList } from '@/apis/news'
import type { NewsListResp } from '@/types/news'
import { ElMessage } from 'element-plus'
import moment from 'moment'
import { Search } from '@element-plus/icons-vue'

const news = ref<NewsListResp['records']>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const loading = ref(false)
const searchKeyword = ref('')
const sortField = ref('publishTime')
const sortOrder = ref('desc')

const formatDate = (date: string) => {
  return moment(date).format('YYYY年M月D日H:mm:ss')
}

const searchNews = async () => {
  try {
    loading.value = true
    const searchParams = {
      pageNumber: currentPage.value,
      pageSize: pageSize.value,
      sortField: sortField.value,
      sortOrder: sortOrder.value,
      news: searchKeyword.value
        ? {
            topic: searchKeyword.value,
          }
        : undefined,
    }

    console.log('发送请求参数:', searchParams)
    const res = await getNewsList(searchParams)
    console.log('收到响应数据:', res)

    if (res.code === 0) {
      console.log('原始数据:', res.data)
      if (res.data && Array.isArray(res.data.records)) {
        news.value = res.data.records
        total.value = res.data.total || res.data.records.length
        console.log('更新表格数据:', news.value)
      } else {
        console.error('无法解析的数据结构:', res.data)
        ElMessage.warning('返回的数据格式不正确')
        news.value = []
        total.value = 0
      }
    } else {
      console.warn('响应状态异常:', res)
      ElMessage.warning(res.message || '未获取到数据')
      news.value = []
      total.value = 0
    }
  } catch (err) {
    console.error('请求发生错误:', err)
    ElMessage.error('获取新闻列表失败：' + (err as Error).message)
    news.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  searchNews()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  searchNews()
}

const handleSearch = () => {
  currentPage.value = 1
  searchNews()
}

const handleSort = ({ prop, order }: { prop: string; order: string | null }) => {
  console.log('排序变化:', { prop, order })
  sortField.value = prop
  sortOrder.value = order === 'ascending' ? 'asc' : order === 'descending' ? 'desc' : ''
  console.log('更新排序参数:', { sortField: sortField.value, sortOrder: sortOrder.value })
  searchNews()
}

const handleNewsClick = (row: NewsListResp['records'][0]) => {
  if (row.url) {
    window.open(row.url, '_blank')
  } else {
    ElMessage.warning('该新闻没有可用的链接')
  }
}

onMounted(() => {
  searchNews()
})
</script>

<template>
  <div class="news-list">
    <div class="search-condition">
      <div class="search-container">
        <div class="search-item">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索新闻标题"
            class="search-input"
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
            <template #append>
              <el-button type="primary" @click="handleSearch">搜索</el-button>
            </template>
          </el-input>
        </div>
      </div>
    </div>

    <el-table
      v-loading="loading"
      :data="news"
      :key="JSON.stringify({ sortField, sortOrder, currentPage, pageSize })"
      style="width: 100%"
      border
      @sort-change="handleSort"
    >
      <el-table-column prop="topic" label="标题" min-width="200" show-overflow-tooltip>
        <template #default="{ row }">
          <el-link type="primary" @click="handleNewsClick(row)">
            {{ row.topic }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column prop="writer" label="作者" min-width="150" show-overflow-tooltip />
      <el-table-column prop="introduction" label="简介" min-width="200" show-overflow-tooltip />
      <el-table-column prop="star" label="Stars" min-width="120" sortable="custom">
        <template #default="{ row }">
          {{ row.star }}
        </template>
      </el-table-column>
      <el-table-column prop="likesNum" label="点赞数" min-width="120" sortable="custom">
        <template #default="{ row }">
          {{ row.likesNum }}
        </template>
      </el-table-column>
      <el-table-column prop="publishTime" label="发布时间" min-width="160" sortable="custom">
        <template #default="{ row }">
          {{ formatDate(row.publishTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="collectTime" label="采集时间" min-width="160" sortable="custom">
        <template #default="{ row }">
          {{ formatDate(row.collectTime) }}
        </template>
      </el-table-column>
    </el-table>

    <div v-if="!loading && (!news || news.length === 0)" class="no-data">暂无数据</div>

    <div v-if="total > 0" class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
.news-list {
  padding: 20px;

  .search-condition {
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

    .search-container {
      display: flex;
      gap: 16px;
      align-items: center;
      flex-wrap: wrap;

      .search-item {
        flex: 1;
        min-width: 300px;

        .search-input {
          :deep(.el-input__wrapper) {
            box-shadow: 0 0 0 1px #dcdfe6 inset;
            border-radius: 8px;
            
            &:hover {
              box-shadow: 0 0 0 1px #409EFF inset;
            }
            
            &.is-focus {
              box-shadow: 0 0 0 1px #409EFF inset;
            }
          }

          :deep(.el-input__prefix) {
            color: #909399;
          }

          :deep(.el-input-group__append) {
            background-color: #409EFF;
            border-color: #409EFF;
            color: white;
            padding: 0 20px;
            
            &:hover {
              background-color: #66b1ff;
              border-color: #66b1ff;
            }
          }
        }
      }
    }
  }

  .no-data {
    text-align: center;
    padding: 20px;
    color: #909399;
  }

  .pagination {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
