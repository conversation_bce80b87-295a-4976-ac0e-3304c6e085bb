<script lang="ts" setup>
import { defineProps, onMounted, ref } from 'vue'
import { getRepoDetail } from '@/apis/repo'
import { getTagNames, addTagAndRepoTag, getPendingTagNames, deleteTagAndRepoTag } from '@/apis/tag'
import { getScoreAverage, addScore } from '@/apis/score'
import type { RepoDetail } from '@/types/repo'
import { marked } from 'marked'
import { ElMessage } from 'element-plus'

const props = defineProps({
  id: String,
})
const repoDetail = ref<RepoDetail>()
const readmeHtmlContent = ref('')
const tags = ref<string[]>([])
const userTags = ref<string[]>([])
const availableTags = ref([
  '标签1',
  '标签2',
  '标签3',
  '标签4',
  '标签5',
  '标签6',
  '标签7',
  '标签8',
  '标签9',
  '标签10'
])
const pendingTags = ref<string[]>([])
const selectedTag = ref('')
const customTag = ref('')
const projectScore = ref(0)
const showScoreDialog = ref(false)
const scoreForm = ref({
  technologicalLevel: 0,
  documentIntegrity: 0,
  communityActivityLevel: 0,
  codeQuality: 0
})

const getData = async () => {
  const { id } = props
  if (id !== undefined) {
    const res = await getRepoDetail(Number(id))
    repoDetail.value = res.data
    setTags(res.data?.tagString)
    setReadmeContent(res.data?.readmeContent)
    await fetchUserTags(Number(id))
    await fetchPendingTags(Number(id))
  } else {
    console.error('id is undefined')
  }
}
const setTags = (tagString: string | undefined) => {
  if (tagString === undefined) {
    return
  }
  tags.value = tagString.split('|')
}
const setReadmeContent = (content: string | undefined) => {
  if (content === undefined) {
      return
    }
    const markdownHtml = marked.parse(atob(content))
    if (typeof markdownHtml === 'string') {
      readmeHtmlContent.value = markdownHtml
    } else {
      console.error('readmeContent is not string')
    }
}
const handleAddTag = async () => {
  if (!selectedTag.value) {
    ElMessage.warning('请选择一个标签')
    return
  }
  if (pendingTags.value.includes(selectedTag.value)) {
    ElMessage.warning('本标签已重复')
    return
  }
  try {
    const { id } = props
    if (!id) throw new Error('repoId 未知')
    await addTagAndRepoTag(Number(id), selectedTag.value)
    await fetchPendingTags(Number(id))
    ElMessage.success('标签已提交，等待审核')
    selectedTag.value = ''
  } catch (e) {
    ElMessage.error('提交标签失败')
  }
}
const handleAddCustomTag = async () => {
  const tag = customTag.value.trim()
  if (!tag) {
    ElMessage.warning('请输入标签内容')
    return
  }
  if (pendingTags.value.includes(tag)) {
    ElMessage.warning('本标签已重复')
    return
  }
  try {
    const { id } = props
    if (!id) throw new Error('repoId 未知')
    await addTagAndRepoTag(Number(id), tag)
    await fetchPendingTags(Number(id))
    ElMessage.success('标签已提交，等待审核')
    customTag.value = ''
  } catch (e) {
    ElMessage.error('提交标签失败')
  }
}
const handleRemoveTag = async (tag: string) => {
  const { id } = props
  if (!id) return
  try {
    await deleteTagAndRepoTag(Number(id), tag)
    await fetchPendingTags(Number(id))
    ElMessage.success('标签已取消')
  } catch (e) {
    ElMessage.error('取消标签失败')
  }
}
const handleScore = () => {
  showScoreDialog.value = true
}
const calculateAverageScore = async () => {
  const { id } = props
  if (!id) return
  const params = {
    repoId: Number(id),
    technologicalLevel: scoreForm.value.technologicalLevel,
    documentIntegrity: scoreForm.value.documentIntegrity,
    communityActivityLevel: scoreForm.value.communityActivityLevel,
    codeQuality: scoreForm.value.codeQuality
  }
  const res = await addScore(params)
  if (res.code === 0) {
    ElMessage.success('评分成功')
    showScoreDialog.value = false
    await fetchScoreAverage(Number(id))
  } else {
    ElMessage.error('评分失败')
  }
}
const fetchUserTags = async (repoId: number) => {
  try {
    const res = await getTagNames(repoId)
    userTags.value = res.data
  } catch (error) {
    console.error('Failed to fetch user tags:', error)
    ElMessage.error('获取用户标签失败')
  }
}
const fetchPendingTags = async (repoId: number) => {
  try {
    const res = await getPendingTagNames(repoId)
    pendingTags.value = res.data
  } catch (error) {
    console.error('Failed to fetch pending tags:', error)
    ElMessage.error('获取审核中标签失败')
  }
}
const fetchScoreAverage = async (repoId: number) => {
  const res = await getScoreAverage(repoId)
  if (res.code === 0 && res.data) {
    scoreForm.value.technologicalLevel = Math.round(res.data.technologicalLevel)
    scoreForm.value.documentIntegrity = Math.round(res.data.documentIntegrity)
    scoreForm.value.communityActivityLevel = Math.round(res.data.communityActivityLevel)
    scoreForm.value.codeQuality = Math.round(res.data.codeQuality)
    const avg = (
      res.data.technologicalLevel +
      res.data.documentIntegrity +
      res.data.communityActivityLevel +
      res.data.codeQuality
    ) / 4
    projectScore.value = Number(avg.toFixed(1))
  }
}
onMounted(() => {
  getData()
  if (props.id) fetchScoreAverage(Number(props.id))
})
</script>
<template>
  <div>
    <div id="container">
      <main id="main">
        <h1>
          <a :href="repoDetail?.htmlUrl" target="_blank">
            {{ repoDetail?.name }}
          </a>
        </h1>
        <el-descriptions :border="true" label-width="100px">
          <el-descriptions-item label="Star">
            {{ repoDetail?.star }}
          </el-descriptions-item>
          <el-descriptions-item label="Watch">
            {{ repoDetail?.watch }}
          </el-descriptions-item>
          <el-descriptions-item label="Fork">
            {{ repoDetail?.fork }}
          </el-descriptions-item>
          <el-descriptions-item label="近半年 commits 量">
            {{ repoDetail?.recentCommits }}
          </el-descriptions-item>
          <el-descriptions-item label="近半年 issues 量">
            {{ repoDetail?.recentIssues }}
          </el-descriptions-item>
          <el-descriptions-item label="近半年 PR 量">
            {{ repoDetail?.recentPr }}
          </el-descriptions-item>
          <el-descriptions-item label="项目标签">
            <el-tag v-for="tag in tags" :key="tag">{{ tag }}</el-tag>
          </el-descriptions-item>
        </el-descriptions>
        <article>
          <h3>README</h3>
          <div v-html="readmeHtmlContent"></div>
        </article>
      </main>
      <aside id="aside">
          <h2>简介</h2>
          <p>{{ repoDetail?.description }}</p>
          <h2>收录时间</h2>
          <p>{{ repoDetail?.collectionTime }}</p>
          <h2>用户评价</h2>
          <div class="user-tags-container">
            <div class="user-tags">
              <el-tag
                v-for="tag in userTags"
                :key="tag"
                class="user-tag"
                type="success"
                effect="plain"
              >
                {{ tag }}
              </el-tag>
            </div>
          </div>
          
          <h2>我要评价</h2>
          <div class="evaluation-input">
            <el-select
              v-model="selectedTag"
              placeholder="请选择标签"
              class="tag-select"
            >
              <el-option
                v-for="tag in availableTags"
                :key="tag"
                :label="tag"
                :value="tag"
              />
            </el-select>
            <el-button type="primary" @click="handleAddTag">确定</el-button>
          </div>
          
          <div class="evaluation-input">
            <el-input
              v-model="customTag"
              placeholder="自定义标签"
              @keyup.enter="handleAddCustomTag"
            >
              <template #append>
                <el-button type="primary" @click="handleAddCustomTag">确定</el-button>
              </template>
            </el-input>
          </div>
          
          <h2>审核中</h2>
          <div class="pending-tags-container">
            <div class="pending-tags">
              <el-tooltip
                v-for="tag in pendingTags"
                :key="tag"
                content="右键取消"
                placement="top"
                effect="dark"
                :show-after="0"
              >
                <el-tag
                  class="pending-tag"
                  type="warning"
                  effect="plain"
                  @contextmenu.prevent="handleRemoveTag(tag)"
                >
                  {{ tag }}
                </el-tag>
              </el-tooltip>
            </div>
          </div>

          <h2>项目评分</h2>
          <div class="score-section">
            <div class="score-container">
              <div class="score-circle">
                <span class="score">{{ projectScore }}</span>
              </div>
            </div>
            <el-button type="primary" @click="handleScore">我要评分</el-button>
          </div>

          <el-dialog
            v-model="showScoreDialog"
            title="项目评分"
            width="400px"
            :close-on-click-modal="false"
          >
            <div class="score-form">
              <div class="score-item">
                <span class="label">技术水平</span>
                <el-radio-group v-model="scoreForm.technologicalLevel">
                  <el-radio :label="1">1分</el-radio>
                  <el-radio :label="2">2分</el-radio>
                  <el-radio :label="3">3分</el-radio>
                  <el-radio :label="4">4分</el-radio>
                  <el-radio :label="5">5分</el-radio>
                </el-radio-group>
              </div>
              
              <div class="score-item">
                <span class="label">文档完整性</span>
                <el-radio-group v-model="scoreForm.documentIntegrity">
                  <el-radio :label="1">1分</el-radio>
                  <el-radio :label="2">2分</el-radio>
                  <el-radio :label="3">3分</el-radio>
                  <el-radio :label="4">4分</el-radio>
                  <el-radio :label="5">5分</el-radio>
                </el-radio-group>
              </div>
              
              <div class="score-item">
                <span class="label">社区活跃度</span>
                <el-radio-group v-model="scoreForm.communityActivityLevel">
                  <el-radio :label="1">1分</el-radio>
                  <el-radio :label="2">2分</el-radio>
                  <el-radio :label="3">3分</el-radio>
                  <el-radio :label="4">4分</el-radio>
                  <el-radio :label="5">5分</el-radio>
                </el-radio-group>
              </div>
              
              <div class="score-item">
                <span class="label">代码质量</span>
                <el-radio-group v-model="scoreForm.codeQuality">
                  <el-radio :label="1">1分</el-radio>
                  <el-radio :label="2">2分</el-radio>
                  <el-radio :label="3">3分</el-radio>
                  <el-radio :label="4">4分</el-radio>
                  <el-radio :label="5">5分</el-radio>
                </el-radio-group>
              </div>
            </div>
            <template #footer>
              <span class="dialog-footer">
                <el-button @click="showScoreDialog = false">取消</el-button>
                <el-button type="primary" @click="calculateAverageScore">确定</el-button>
              </span>
            </template>
          </el-dialog>
      </aside>
    </div>
  </div>
</template>
<style lang="scss" scoped>
#container {
  display: flex;
  #main {
    flex: 1;
    margin-right: 16px;
  }
  #aside {
    width: 300px;
    padding-top: 48px;
  }
}
.user-tags-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 8px;
  margin-top: 8px;
  height: 160px;
  overflow-y: auto;
}
.user-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}
.user-tag {
  margin-right: 8px;
  margin-bottom: 8px;
  border: 1px solid #dcdfe6;
}
.user-tags-container::-webkit-scrollbar {
  width: 6px;
}
.user-tags-container::-webkit-scrollbar-thumb {
  background-color: #909399;
  border-radius: 3px;
}
.user-tags-container::-webkit-scrollbar-track {
  background-color: #f5f7fa;
}
.evaluation-input {
  display: flex;
  gap: 8px;
  margin: 8px 0;
  
  .tag-select {
    flex: 1;
  }

  :deep(.el-input-group__append) {
    padding: 0;
    .el-button {
      margin: 0;
      border-radius: 0 4px 4px 0;
      background-color: #409eff;
      border-color: #409eff;
      color: white;
      
      &:hover {
        background-color: #66b1ff;
        border-color: #66b1ff;
      }
    }
  }
}
.pending-tags-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 8px;
  margin-top: 8px;
  height: 80px;
  overflow-y: auto;
}
.pending-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}
.pending-tag {
  margin-right: 8px;
  margin-bottom: 8px;
  border: 1px solid #dcdfe6;
  cursor: pointer;
}
.pending-tags-container::-webkit-scrollbar {
  width: 6px;
}
.pending-tags-container::-webkit-scrollbar-thumb {
  background-color: #909399;
  border-radius: 3px;
}
.pending-tags-container::-webkit-scrollbar-track {
  background-color: #f5f7fa;
}
:deep(.el-tooltip__popper) {
  color: #ff0000 !important;
}
.score-section {
  display: flex;
  align-items: center;
  gap: 16px;
  margin: 16px 0;
}
.score-container {
  display: flex;
  justify-content: center;
}
.score-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 2px solid #409eff;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
  
  .score {
    font-size: 24px;
    font-weight: bold;
    color: #409eff;
  }
}
.score-form {
  .score-item {
    margin-bottom: 20px;
    
    .label {
      display: block;
      margin-bottom: 8px;
      font-weight: bold;
    }
    
    :deep(.el-radio-group) {
      display: flex;
      justify-content: space-between;
      width: 100%;
    }
  }
}
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}
</style>
