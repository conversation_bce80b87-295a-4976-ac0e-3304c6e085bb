<script setup lang="ts">
import { onMounted, ref, computed, watch, nextTick, onBeforeUnmount } from 'vue'
import { getRepoList } from '@/apis/repo'
import type { RepoListResp, RepoListReqParams } from '@/types/repo'
import { ElMessage } from 'element-plus'
import { formatDate } from '@/utils/date'
import api from '@/utils/axios'
import { ChatDotRound, Search } from '@element-plus/icons-vue'
import { WatchDirectoryFlags } from 'typescript'
import { getRepoTags } from '@/apis/repo'

const repos = ref<RepoListResp['records']>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const loading = ref(false)
const searchKeyword = ref('')
const tagSearchKeyword = ref('')
const sortField = ref('star')
const sortOrder = ref('desc')

interface Tag {
  id: number
  name: string
  color: string
}

const selectedTags = ref<Tag[]>([])
const inputValue = ref('')
const suggestions = ref<Tag[]>([])
const cursorIndex = ref(-1)
const currentTagPage = ref(1)
const showSuggestions = ref(false)
const isLoading = ref(false)
const noMore = ref(false)
const maxSelect = 5
const inputRef = ref<HTMLInputElement | null>(null)
const suggestionListRef = ref<HTMLElement | null>(null)
const boxRef = ref<HTMLElement | null>(null)
const mockTags = ref<string[]>(['标签一', '标签二', '标签三', '标签四', '标签二', '标签三'])

const options = ref<Tag[]>([])
const value = ref([])
const list = ref<Tag[]>([])

const tags = [
  'Alabama',
  'Alaska',
  'Arizona',
  'Arkansas',
  'California',
  'Colorado',
  'Connecticut',
  'Delaware',
  'Florida',
  'Georgia',
  'Hawaii',
  'Idaho',
  'Illinois',
  'Indiana',
  'Iowa',
  'Kansas',
  'Kentucky',
  'Louisiana',
  'Maine',
  'Maryland',
  'Massachusetts',
  'Michigan',
  'Minnesota',
  'Mississippi',
  'Missouri',
  'Montana',
  'Nebraska',
  'Nevada',
  'New Hampshire',
  'New Jersey',
  'New Mexico',
  'New York',
  'North Carolina',
  'North Dakota',
  'Ohio',
  'Oklahoma',
  'Oregon',
  'Pennsylvania',
  'Rhode Island',
  'South Carolina',
  'South Dakota',
  'Tennessee',
  'Texas',
  'Utah',
  'Vermont',
  'Virginia',
  'Washington',
  'West Virginia',
  'Wisconsin',
  'Wyoming',
]
let debounceTimer: any

// 聊天机器人相关
const chatVisible = ref(false)
const chatInput = ref('')
const chatMessages = ref<Array<{ type: 'user' | 'bot'; content: string }>>([])
const chatLoading = ref(false)

const sendMessage = async () => {
  if (!chatInput.value.trim()) return

  // 添加用户消息
  chatMessages.value.push({
    type: 'user',
    content: chatInput.value
  })

  const userMessage = chatInput.value
  chatInput.value = ''
  chatLoading.value = true

  try {
    const response = await api.get<string>('/ai/chat', {
      params: {
        input: userMessage
      }
    })

    // 添加机器人回复
    chatMessages.value.push({
      type: 'bot',
      content: response.data
    })
  } catch (error) {
    console.error('发送消息失败:', error)
    ElMessage.error('发送消息失败：' + (error as Error).message)
  } finally {
    chatLoading.value = false
  }
}

const toggleChat = () => {
  chatVisible.value = !chatVisible.value
}

const searchRepos = async () => {
  try {
    loading.value = true
    const searchParams: RepoListReqParams = {
      pageNumber: currentPage.value,
      pageSize: pageSize.value,
      sortField: sortField.value,
      sortOrder: sortOrder.value,
    }
    if (searchKeyword.value) {
      searchParams.repository = searchParams.repository || {}
      searchParams.repository.name = searchKeyword.value
    }
    if (tagSearchKeyword.value) {

      const response = await getRepoTags({
        tag: { name: tagSearchKeyword.value },
        pageNumber: currentPage.value,
        pageSize: pageSize.value,
        sortOrder: sortOrder.value
      })


      if (Number(response.code) === 0) {
        if (response.data && Array.isArray(response.data.records)) {
          repos.value = response.data.records
          total.value = response.data.total || response.data.records.length
        } else {
          ElMessage.warning('返回的数据格式不正确')
          repos.value = []
          total.value = 0
        }
      } else {
        ElMessage.warning(response.message || '未获取到数据')
        repos.value = []
        total.value = 0
      }
      return
    }

    const res = await getRepoList(searchParams)

    if (res.code === 0) {
      if (res.data && Array.isArray(res.data.records)) {
        repos.value = res.data.records
        total.value = res.data.total || res.data.records.length
      } else {
        console.error('无法解析的数据结构:', res.data)
        ElMessage.warning('返回的数据格式不正确')
        repos.value = []
        total.value = 0
      }
    } else {
      console.warn('响应状态异常:', res)
      ElMessage.warning(res.message || '未获取到数据')
      repos.value = []
      total.value = 0
    }
  } catch (err) {
    console.error('请求发生错误:', err)
    ElMessage.error('获取仓库列表失败：' + (err as Error).message)
    repos.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  searchRepos()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  searchRepos()
}

const handleSearch = () => {
  currentPage.value = 1
  searchRepos()
}

const handleSort = ({ prop, order }: { prop: string; order: string | null }) => {
  sortField.value = prop
  sortOrder.value = order === 'ascending' ? 'asc' : order === 'descending' ? 'desc' : ''
  searchRepos()
}

// 点击外部时关闭建议列表
function handleClickOutside(event: MouseEvent) {
  const el = boxRef.value
  if (el && !el.contains(event.target as Node)) {
    showSuggestions.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  searchRepos()
  list.value = tags.map((item) => {
    return { id: 1, color: '000000', value: `value:${item}`, name: `label:${item}` }
  })
})

onBeforeUnmount(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<template>
  <div class="search-condition">
    <div class="search-container">
      <div class="search-item">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索仓库名称"
          class="search-input"
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
          <template #append>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
          </template>
        </el-input>
      </div>
      <!-- <div class="search-item">
        <el-select
          v-model="value"
          multiple
          filterable
          remote
          reserve-keyword
          placeholder="选择标签"
          :remote-method="remoteMethod"
          :loading="loading"
          class="tag-select"
        >
          <el-option v-for="item in options" :key="item.name" :label="item.name" :value="item.name">
          </el-option>
        </el-select>
      </div> -->
      <div class="search-item">
        <el-input
          v-model="tagSearchKeyword"
          placeholder="搜索标签名称"
          class="search-input"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
          <template #append>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
          </template>
        </el-input>
      </div>
    </div>
  </div>

  <!-- 聊天机器人按钮 -->
  <div class="chat-button" @click="toggleChat">
    <el-icon><ChatDotRound /></el-icon>
  </div>

  <!-- 聊天窗口 -->
  <el-dialog
    v-model="chatVisible"
    title="AI 助手"
    width="400px"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    class="chat-dialog"
    destroy-on-close
  >
    <div class="chat-container">
      <div class="chat-messages">
        <template v-if="chatMessages.length > 0">
          <div
            v-for="(message, index) in chatMessages"
            :key="index"
            :class="['message', message.type]"
          >
            {{ message.content }}
          </div>
        </template>
        <div v-else class="message system">
          欢迎使用AI助手，请输入您的问题。
        </div>
        <div v-if="chatLoading" class="message bot loading">
          正在思考...
        </div>
      </div>
      <div class="chat-input">
        <el-input
          v-model="chatInput"
          placeholder="请输入您的问题"
          :disabled="chatLoading"
          @keyup.enter="sendMessage"
        >
          <template #append>
            <el-button @click="sendMessage" :loading="chatLoading">发送</el-button>
          </template>
        </el-input>
      </div>
    </div>
  </el-dialog>

  <el-table
    v-loading="loading"
    :data="repos"
    :key="JSON.stringify({ sortField, sortOrder, currentPage, pageSize })"
    style="width: 100%"
    :border="true"
    @sort-change="handleSort"
  >
    <el-table-column prop="name" label="仓库名称" min-width="200">
      <template #default="{ row }">
        <el-link type="primary" :href="`/repo/${row.id}`" target="_blank">
          {{ row.name }}
        </el-link>
        <div class="tags-behind-area">
          <span
            class="tags-behind"
            v-for="(tag, index) in (row.tagList && Array.isArray(row.tagList) && row.tagList.length > 0
              ? row.tagList
              : (row.tagString ? row.tagString.split('|') : []))"
            :key="index"
          >
            {{ tag.name || tag }}
          </span>
        </div>
      </template>
    </el-table-column>
    <el-table-column prop="description" label="描述" min-width="300" show-overflow-tooltip />
    <el-table-column prop="star" label="Stars" width="100" sortable="custom" />
    <el-table-column prop="fork" label="Forks" width="100" sortable="custom" />
    <el-table-column prop="watch" label="Watchers" width="100" sortable="custom" />
    <el-table-column prop="recentCommits" label="最近提交" width="100" sortable="custom" />
    <el-table-column prop="collectionTime" label="采集时间" width="180">
      <template #default="{ row }">
        {{ formatDate(row.collectionTime) }}
      </template>
    </el-table-column>
  </el-table>

  <div v-if="!loading && (!repos || repos.length === 0)" class="no-data">暂无数据</div>

  <div v-if="total > 0" class="pagination">
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      :total="total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handlePageChange"
    />
  </div>
</template>

<style scoped lang="scss">
.search-condition {
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

  .search-container {
    display: flex;
    gap: 16px;
    align-items: center;
    flex-wrap: wrap;

    .search-item {
      flex: 1;
      min-width: 300px;

      .search-input {
        :deep(.el-input__wrapper) {
          box-shadow: 0 0 0 1px #dcdfe6 inset;
          border-radius: 8px;

          &:hover {
            box-shadow: 0 0 0 1px #409EFF inset;
          }

          &.is-focus {
            box-shadow: 0 0 0 1px #409EFF inset;
          }
        }

        :deep(.el-input__prefix) {
          color: #909399;
        }

        :deep(.el-input-group__append) {
          background-color: #409EFF;
          border-color: #409EFF;
          color: white;
          padding: 0 20px;

          &:hover {
            background-color: #66b1ff;
            border-color: #66b1ff;
          }
        }
      }

      .tag-select {
        width: 100%;

        :deep(.el-input__wrapper) {
          box-shadow: 0 0 0 1px #dcdfe6 inset;
          border-radius: 8px;

          &:hover {
            box-shadow: 0 0 0 1px #409EFF inset;
          }

          &.is-focus {
            box-shadow: 0 0 0 1px #409EFF inset;
          }
        }
      }

      .tag-suggestions {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        margin-top: 4px;
        max-height: 200px;
        overflow-y: auto;
        z-index: 1000;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

        .tag-suggestion-item {
          padding: 8px 12px;
          cursor: pointer;

          &:hover {
            background-color: #f5f7fa;
          }
        }
      }
    }
  }
}

.tag {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 14px;
  color: white;
}

.tag .remove {
  margin-left: 6px;
  cursor: pointer;
}

.input {
  flex: 1;
  min-width: 100px;
  border: none;
  outline: none;
  padding: 4px;
  align-items: center;
}

.suggestions {
  max-height: 180px;
  overflow: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-top: 4px;
  list-style: none;
  padding: 0;
  position: absolute;
  background: white;
  z-index: 10;
}

.suggestions li {
  padding: 6px 12px;
  cursor: pointer;
}

.suggestions li.active {
  background-color: #f0f0f0;
}

.no-data {
  text-align: center;
  padding: 20px;
  color: #909399;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
.tags-behind {
  margin: 5px;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  display: inline-block;
  color: white;
  background-color: #a0c8f8;
}
.tags-behind-area {
  margin-top: 5px;
  white-space: nowrap;
  overflow-x: auto;
  display: flex;
  flex-wrap: nowrap;
  gap: 5px;
  padding-bottom: 5px;
  width: 100%;
  position: relative;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}
.tags-behind-area::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.chat-button {
  position: fixed;
  right: 20px;
  bottom: 20px;
  width: 50px;
  height: 50px;
  background-color: #409EFF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
  z-index: 2000;

  .el-icon {
    color: white;
    font-size: 24px;
  }

  &:hover {
    transform: scale(1.1);
  }
}

.chat-dialog {
  .chat-container {
    height: 400px;
    display: flex;
    flex-direction: column;
  }

  .chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
    display: flex;
    flex-direction: column;
    gap: 10px;

    .message {
      max-width: 80%;
      padding: 8px 12px;
      border-radius: 8px;
      word-break: break-word;

      &.user {
        align-self: flex-end;
        background-color: #409EFF;
        color: white;
      }

      &.bot {
        align-self: flex-start;
        background-color: #f4f4f5;
        color: #303133;
      }

      &.system {
        align-self: center;
        background-color: #f4f4f5;
        color: #909399;
        font-size: 14px;
      }

      &.loading {
        color: #909399;
        background-color: transparent;
      }
    }
  }

  .chat-input {
    padding: 10px;
    border-top: 1px solid #dcdfe6;

    .el-input {
      width: 100%;
    }
  }
}
</style>
