<script setup lang="ts">
import { testAPI } from '@/apis/test'
import { onMounted, ref } from 'vue'

const result = ref('error')

const test = async () => {
  try {
    const res = await testAPI()
    if (res.code === 200) {
      result.value = 'success'
    } else {
      result.value = 'error code: ' + res.code
    }
  } catch (error) {
    result.value = 'error:' + error
  }
}

onMounted(() => {
  test()
})
</script>
<template>
  <div>
    <h1>Test</h1>
    <h2>Test result is {{ result }}</h2>
  </div>
</template>
<style scoped lang="scss"></style>
